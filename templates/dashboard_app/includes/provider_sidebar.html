<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_dashboard' %} active{% endif %}" href="{% url 'dashboard_app:provider_dashboard' %}">
        Dashboard
    </a>
</li>

<!-- Venue Creation/Management Section -->
{% if not has_venue %}
<li class="nav-item">
    <a class="nav-link venue-creation-link" href="{% url 'venues_app:venue_create' %}">
        Create Venue
        <span class="badge bg-success ms-2">Start Here</span>
    </a>
</li>
{% else %}
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_venues' %} active{% endif %}" href="{% url 'venues_app:provider_venues' %}">
        My Venue
        {% if venue %}
            {% if venue.approval_status == 'approved' %}
                <span class="badge bg-success ms-2">
                    <i class="fas fa-check"></i>
                </span>
            {% elif venue.approval_status == 'pending' %}
                <span class="badge bg-warning ms-2">
                    <i class="fas fa-clock"></i>
                </span>
            {% elif venue.approval_status == 'rejected' %}
                <span class="badge bg-danger ms-2">
                    <i class="fas fa-times"></i>
                </span>
            {% else %}
                <span class="badge bg-secondary ms-2">
                    <i class="fas fa-edit"></i>
                </span>
            {% endif %}
        {% endif %}
    </a>
</li>
{% endif %}

<!-- Booking Management -->
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_todays_bookings' %} active{% endif %}" href="{% url 'dashboard_app:provider_todays_bookings' %}">
        Today's Bookings
    </a>
</li>

<!-- Analytics & Reports -->
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_earnings_reports' %} active{% endif %}" href="{% url 'dashboard_app:provider_earnings_reports' %}">
        Earnings Reports
    </a>
</li>

<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_service_performance' %} active{% endif %}" href="{% url 'dashboard_app:provider_service_performance' %}">
        Service Performance
    </a>
</li>

<!-- Business Management -->
<li class="nav-item">
    <a class="nav-link{% if 'discount' in request.resolver_match.url_name %} active{% endif %}" href="{% url 'discount_app:provider_discount_list' %}">
        Discounts
    </a>
</li>

<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_team_management' %} active{% endif %}" href="{% url 'dashboard_app:provider_team_management' %}">
        Team Management
    </a>
</li>

<!-- Profile Management -->
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'service_provider_profile' or request.resolver_match.url_name == 'service_provider_profile_edit' %} active{% endif %}" href="{% url 'accounts_app:service_provider_profile' %}">
        Profile Management
    </a>
</li>

<style>
/* Professional sidebar styling with high specificity */
.dashboard-wrapper .dashboard-sidebar {
    width: 280px !important;
    min-width: 280px !important;
}

/* Remove icons and enhance text-only navigation */
.dashboard-wrapper .dashboard-sidebar .nav-link i {
    display: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link {
    font-weight: 500 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.5rem !important;
    margin: 0.125rem 1rem !important;
    border: 1px solid transparent !important;
    transition: all 0.2s ease !important;
    text-align: left !important;
}

/* Remove "Create Venue" special background styling */
.dashboard-wrapper .dashboard-sidebar .venue-creation-link {
    background: transparent !important;
    border-left: none !important;
    font-weight: 500 !important;
    margin: 0.125rem 1rem !important;
    border-radius: 0.5rem !important;
    border: 1px solid transparent !important;
}

.dashboard-wrapper .dashboard-sidebar .venue-creation-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
    transform: none !important;
    transition: all 0.2s ease !important;
}

/* Remove dashboard button special background */
.dashboard-wrapper .dashboard-sidebar .nav-link.active {
    background: transparent !important;
    color: var(--cw-brand-primary) !important;
    border: 1px solid var(--cw-brand-accent) !important;
    font-weight: 600 !important;
    box-shadow: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
}

/* Keep badges and status icons visible */
.dashboard-wrapper .dashboard-sidebar .nav-link .badge {
    font-size: 0.7rem !important;
    display: inline-block !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-check,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-clock,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-times,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-edit {
    display: inline !important;
    opacity: 0.6 !important;
}

/* Adjust main content to accommodate wider sidebar */
.dashboard-wrapper .col-md-9.col-lg-10 {
    flex: 0 0 calc(100% - 280px) !important;
    max-width: calc(100% - 280px) !important;
}
</style>


